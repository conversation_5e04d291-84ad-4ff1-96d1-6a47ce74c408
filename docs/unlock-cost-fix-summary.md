# 牧场区解锁费用修复总结

## 问题描述

之前的牧场区解锁费用逻辑存在错误：
- 每个牧场区的解锁费用都不一样
- 使用了错误的映射关系：`plotNumber N` 使用 `grade N` 的 `cost`

## 正确的逻辑

根据需求，`farm_configs` 中 `grade = 0` 的 `cost` 是每个牧场区的解锁费用，数量都一样：
- 第一个牧场区（plotNumber = 1）：免费解锁（unlockCost = 0）
- 其他所有牧场区（plotNumber 2-20）：都使用 `grade = 0` 的 `cost` 值作为解锁费用

## 修复内容

### 1. 核心逻辑修复

**文件：`src/config/farmPlotConfig.ts`**

- ✅ 修复 `getFarmPlotUnlockCost()` 函数
  - 所有牧场区都使用 `grade = 0` 的 `cost` 值
  - 第一个牧场区保持免费
  - 动态从数据库获取最新配置值
- ✅ 更新降级配置 `FALLBACK_FARM_PLOT_UNLOCK_COST`
  - 支持动态更新，与数据库配置保持同步
  - 添加 `updateFallbackUnlockCost()` 函数
- ✅ 添加 `DEFAULT_UNLOCK_COST` 常量作为默认值

### 2. 服务层修复

**文件：`src/services/farmPlotService.ts`**

- ✅ 移除解锁时动态更新下一个牧场区解锁费用的逻辑
- 现在所有牧场区的解锁费用都是固定的

### 3. 脚本文件更新

**文件：`scripts/fix-unlock-costs.js`**
- ✅ 更新解锁费用计算逻辑

**文件：`scripts/check-unlock-costs.js`**
- ✅ 更新预期解锁费用数组

**文件：`scripts/test-unlock-costs-api.js`**
- ✅ 更新预期解锁费用数组

### 4. 新增文件

**文件：`scripts/test-unlock-cost-logic.js`**
- ✅ 创建逻辑测试脚本，验证修复是否正确
- ✅ 支持测试动态解锁费用

**文件：`scripts/migrate-unlock-costs-to-uniform.js`**
- ✅ 创建数据库迁移脚本，统一现有数据

**文件：`scripts/test-unlock-cost-fix.js`**
- ✅ 创建完整的测试脚本（需要数据库连接）

**文件：`scripts/update-unlock-cost-config.js`**
- ✅ 创建配置更新工具，方便修改解锁费用

## 配置值

- **Grade 0 的 cost 值**: 13096
- **第一个牧场区解锁费用**: 0（免费）
- **其他牧场区解锁费用**: 13096（统一）

## 测试验证

运行测试脚本验证修复：

```bash
# 测试逻辑（不需要数据库）
node scripts/test-unlock-cost-logic.js

# 测试完整功能（需要数据库）
node scripts/test-unlock-cost-fix.js
```

## 配置管理

### 更新解锁费用配置

如果需要修改 `farm_configs` 表中的解锁费用：

```bash
# 预览模式（查看当前值和将要更新的值）
node scripts/update-unlock-cost-config.js 15000

# 执行实际更新
node scripts/update-unlock-cost-config.js 15000 --execute
```

### 数据库迁移

如果需要修复现有数据库中的解锁费用：

```bash
# 预览模式（不会实际更新）
node scripts/migrate-unlock-costs-to-uniform.js

# 执行实际迁移
node scripts/migrate-unlock-costs-to-uniform.js --execute
```

## 影响范围

### 直接影响
- 所有牧场区的解锁费用现在都统一为 13096（除了第一个免费）
- 用户解锁牧场区的成本更加一致和可预测

### 间接影响
- 游戏经济平衡可能需要调整
- 用户体验更加一致

## 注意事项

1. **数据一致性**: 现有数据库中的解锁费用可能需要迁移
2. **测试**: 建议在测试环境中先验证修复效果
3. **监控**: 部署后需要监控解锁费用相关的功能是否正常

## 相关文件清单

### 修改的文件
- `src/config/farmPlotConfig.ts`
- `src/services/farmPlotService.ts`
- `scripts/fix-unlock-costs.js`
- `scripts/check-unlock-costs.js`
- `scripts/test-unlock-costs-api.js`

### 新增的文件
- `scripts/test-unlock-cost-logic.js`
- `scripts/migrate-unlock-costs-to-uniform.js`
- `scripts/test-unlock-cost-fix.js`
- `scripts/update-unlock-cost-config.js`
- `docs/unlock-cost-fix-summary.md`

## 验证结果

✅ 逻辑测试通过 - 所有牧场区的解锁费用都正确
✅ 降级配置正确 - 动态获取配置值
✅ 边界条件测试通过 - 错误输入正确抛出异常
✅ 动态配置测试通过 - 支持修改数据库配置后自动更新
✅ 初始化测试通过 - 创建农场区块时正确使用数据库配置

## 动态配置特性

现在系统支持动态配置解锁费用：

1. **自动同步**: 代码会自动从数据库获取最新的 `grade = 0` 的 `cost` 值
2. **降级方案更新**: 当成功获取数据库配置时，会自动更新降级配置
3. **配置工具**: 提供专门的工具脚本来修改解锁费用配置
4. **向后兼容**: 如果数据库不可用，会使用默认值 13096

这意味着您可以随时修改 `farm_configs` 表中 `grade = 0` 的 `cost` 值，系统会自动使用新的值。
